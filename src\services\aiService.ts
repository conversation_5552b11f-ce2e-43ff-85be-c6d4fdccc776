import { Message } from '../types';

// Mock AI service - in a real app, this would connect to actual AI APIs
export class AIService {
  private static readonly MOCK_RESPONSES = [
    "I understand your question. Let me provide a comprehensive response that addresses your specific needs.",
    "That's an interesting point! Here's how I would approach this problem step by step.",
    "Based on the information you've provided, I can offer several insights and recommendations.",
    "This is a great question that touches on several important concepts. Let me break it down for you.",
    "I'd be happy to help you with that! Here's what I think would work best in this situation."
  ];

  static async generateResponse(
    messages: Message[],
    model: string
  ): Promise<string> {
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

    const lastMessage = messages[messages.length - 1];
    
    // Simple mock logic based on user input
    if (lastMessage.content.toLowerCase().includes('code')) {
      return `Here's a code example for your request:\n\n\`\`\`python\ndef example_function():\n    return "Hello, <PERSON>!"\n\nresult = example_function()\nprint(result)\n\`\`\`\n\nThis demonstrates the basic concept you asked about using ${model}.`;
    }

    if (lastMessage.content.toLowerCase().includes('help')) {
      return `I'm here to help! You can ask me about:\n\n• Programming and software development\n• General knowledge questions\n• Creative writing and brainstorming\n• Problem-solving and analysis\n\nWhat specific area would you like assistance with?`;
    }

    // Return a random mock response
    const randomResponse = this.MOCK_RESPONSES[
      Math.floor(Math.random() * this.MOCK_RESPONSES.length)
    ];

    return `${randomResponse}\n\n*Response generated by ${model}*`;
  }
}